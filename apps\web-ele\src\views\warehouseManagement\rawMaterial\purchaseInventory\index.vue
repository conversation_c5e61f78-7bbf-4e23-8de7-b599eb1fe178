<script lang="ts" setup>
import { h, onMounted, ref } from 'vue';

import { globalShareState, PageCard, prompt } from '@vben/common-ui';

import { ElMessageBox, ElNotification } from 'element-plus';

import { GetPurchaseOrderList } from '#/api/purchase/imported';
import { confirmPuchaseInbound } from '#/api/warehouse/inbound/inbound';
import ServerGridComponent from '#/components/ag-grid/ServerGridComponent.vue';
import { $t } from '#/locales';
import selectData from '#/views/common/selectData.vue';

import ImportedDetail from './components/ImportedDetail.vue';
import QueryForm from './components/QueryForm.vue';

/**
 * 组件引用
 * importedDetailRef: 详情弹窗组件引用
 * importImportedRef: 导入弹窗组件引用
 * gridRef: 表格组件引用
 */
const importedDetailRef = ref();
const gridRef = ref<any>(null);
const selectDataRef = ref();

// 全局变量存储选中的仓库代码
const selectedWarehouseCode = ref('');

/**
 * 数据状态
 * searchParams: 搜索参数
 */
const searchParams = ref({});

/**
 * 表格列定义
 * 定义表格的列结构，包括列标题、字段名、排序和筛选等配置
 */
const columnDefs: any[] = [
  {
    headerName: $t('purchase.importOrderNo'),
    field: 'orderNo',
    sortable: true, // 启用排序
  },
  {
    headerName: $t('purchase.orderDate'),
    field: 'orderDate',
    filterType: 'date', // 使用 filterType 避免与 AG-Grid 的 type 字段冲突
    sortable: true, // 启用排序
  },
  {
    headerName: $t('purchase.orderStatus'),
    field: 'orderStatus',
    sortable: true, // 启用排序
    valueFormatter: (params: any) => {
      const statusMap: Record<string, string> = {
        new: $t('purchase.new'),
        confirm: $t('purchase.confirm'),
        store: $t('purchase.store'),
      };
      return statusMap[params.value] || params.value;
    },
  },
  {
    headerName: $t('purchase.poNo'),
    field: 'poNo',
    sortable: true, // 启用排序
  },
  {
    headerName: $t('purchase.declarationNo'),
    field: 'declarationNo',
    sortable: true, // 启用排序
  },
  {
    headerName: $t('purchase.supplier'),
    field: 'supplier',
    sortable: true, // 启用排序
  },
  {
    headerName: $t('purchase.receiptDate'),
    field: 'receiptDate',
    sortable: true, // 启用排序
  },
  {
    headerName: $t('purchase.confirmUserName'),
    field: 'confirmUserName',
    sortable: true, // 启用排序
  },
  {
    headerName: $t('purchase.confirmDateTime'),
    field: 'confirmDateTime',
    sortable: true, // 启用排序
  },
  {
    headerName: $t('production.Action'),
    field: 'action', // 注意这里field不是实际的数据字段，仅用于标识
    pinned: 'right',
    cellRenderer: 'actionCell',
    // flex: 1,
    cellRendererParams: {
      actions: {
        label: '...',
        primaryActions: [
          {
            label: $t('basic.view'),
            callback: (data: any) => {
              handleEdit(data, 'view');
            },
            auth: ['purchase.local.view'],
            type: 'primary',
            size: 'small',
          },
          {
            label: $t('warehouse.rawMaterial.confirmInv'),
            callback: (data: any) => {
              handleConfirmOrder(data);
            },
            // 如果当前行状态是 入库状态，置灰
            disabled: (data: any) => data.orderStatus !== 'confirm',
            // 添加权限
            auth: ['rawMaterial.purchaseInv.confirm'],
            type: 'success',
            size: 'small',
          },
        ],
      },
    },
  },
];

/**
 * 搜索参数处理函数
 * 添加默认查询条件
 */
const searchParamsProcessor = (params: any) => {
  return {
    orderStatus: 'confirm', // 默认查询已确认的订单
    ...params,
  };
};

/**
 * 处理编辑或查看操作
 * 打开详情弹窗，根据pageType设置为编辑或查看模式
 * @param data 当前行数据
 * @param pageType 页面类型，'edit'为编辑，'view'为查看
 */
const handleEdit = (data: any, pageType: string) => {
  importedDetailRef.value.modalApi.setData({
    record: data,
    isUpdate: pageType === 'edit',
    isView: pageType === 'view',
  });
  importedDetailRef.value.modalApi.open();
  // 关闭后刷新数据
  importedDetailRef.value.modalApi.setState({
    onClosed: () => {
      gridRef.value?.handleRefresh();
    },
  });
};
const selectConfig = {
  api: '/basic/getWarehouseList',
  params: { codeType: 'RM' },
  columns: [
    {
      headerName: $t('warehouse.warehouseCode'),
      field: 'warehouseCode',
      width: 140,
    },
    {
      headerName: $t('warehouse.warehouseName'),
      field: 'warehouseName',
      width: 140,
    },
  ],
  title: $t('basic.pleaseSelect'),
  showSearch: true,
  immediate: false,
  searchPlaceholder: $t('basic.selectInput'),
  multiple: false,
  class: 'w-[50%] h-[70%] min-w-[600px]',
};
const selectOpen = async (updateCallback?: ((val: string) => void) | null) => {
  try {
    // 清空之前的选择数据
    selectDataRef.value.modalApi.sharedData = null;

    selectDataRef.value.modalApi.open();

    // 监听弹窗关闭事件
    selectDataRef.value.modalApi.onClosed = () => {
      const selectedData = selectDataRef.value.modalApi.sharedData;
      // 只有当用户确认选择并且有数据时才执行更新
      if (selectedData && selectedData.length > 0) {
        const warehouse = selectedData[0];
        const warehouseCode = warehouse.warehouseCode || '';

        // 更新全局变量
        selectedWarehouseCode.value = warehouseCode;

        // 如果有更新回调函数，直接调用它
        if (updateCallback) {
          updateCallback(warehouseCode);
        }
      }
    };
  } catch (error) {
    console.error('Error in selectOpen:', error);
    ElNotification({
      type: 'error',
      message: '打开选择窗口失败',
      duration: 2500,
    });
  }
};

/**
 * 处理确认订单操作
 * 将订单状态从"新建"更改为"确认"
 * @param data 要确认的订单数据
 */
const handleConfirmOrder = async (data: any) => {
  try {
    const components = globalShareState.getComponents().SearchInput;

    // 创建一个响应式的值来存储当前输入
    const currentValue = ref('');
    let updateModelValue: ((val: string) => void) | null = null;

    prompt({
      icon: 'question',
      content: '请输入仓库代码：',
      component: (props: any) => {
        // 保存更新函数的引用
        updateModelValue = props['onUpdate:modelValue'];

        return h(components, {
          type: 'text',
          modelValue: props.modelValue, // 绑定当前值
          'onUpdate:modelValue': (val: string) => {
            currentValue.value = val;
            props['onUpdate:modelValue']?.(val);
          },
        });
      },
      componentProps: {
        placeholder: $t('warehouse.selectWarehouseCode'),
        searchButtonProps: {
          type: 'primary',
        },
        onSearch: async () => {
          try {
            // 将更新函数传递给 selectOpen
            await selectOpen(updateModelValue);
          } catch (error) {
            console.error('选择仓库失败:', error);
          }
        },
      },
      defaultValue: '',
    })
      .then(async (val) => {
        if (val) {
          // 弹出确认对话框
          await ElMessageBox.confirm(
            $t('warehouse.rawMaterial.confirmInventoryThisOrder'),
            $t('basic.tips'),
            {
              confirmButtonText: $t('basic.confirm'),
              cancelButtonText: $t('basic.cancel'),
              type: 'warning',
            },
          ).then(async () => {
            // 调用确认入库的API接口
            await confirmPuchaseInbound({
              warehouseCode: val,
              id: data.id,
            });
            ElNotification({
              duration: 2500,
              message: $t('warehouse.storeSuccess'),
              type: 'success',
            });
            gridRef.value?.handleRefresh(); // 确认成功后刷新数据
          });
        } else {
          // 弹窗提醒，需要输入仓库代码
          // 只需要弹窗提醒啊
          ElNotification({
            duration: 2500,
            message: $t('warehouse.enterWarehouseCode'),
            type: 'warning',
          });
        }
      })
      .catch(() => {});
  } catch (error) {
    if (error !== 'cancel') {
      console.error($t('purchase.confirmOrderFailed'), error);
      ElNotification({
        duration: 2500,
        message: $t('purchase.confirmOrderFailed'),
        type: 'error',
      });
    }
  }
};

/**
 * 处理搜索操作
 * 更新搜索参数并重新获取数据
 * @param formData 表单数据
 */
const handleSearch = (formData: any) => {
  searchParams.value = formData;
  gridRef.value?.search(formData);
};

/**
 * 组件挂载时执行
 */
onMounted(() => {
  // ServerGridComponent 会自动加载数据，searchParamsProcessor 会添加默认条件
});
</script>

<template>
  <PageCard auto-content-height>
    <!-- 查询表单组件 -->
    <QueryForm @search="handleSearch" />

    <!-- 数据表格 -->
    <ServerGridComponent
      ref="gridRef"
      :column-defs="columnDefs"
      :api-function="GetPurchaseOrderList"
      :search-params-processor="searchParamsProcessor"
      :page-size="10"
    />

    <!-- 详情弹窗组件 -->
    <ImportedDetail ref="importedDetailRef" />

    <selectData ref="selectDataRef" v-bind="selectConfig" />
  </PageCard>
</template>
